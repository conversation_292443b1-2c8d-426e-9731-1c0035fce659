import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { createDb } from "~/lib/db/db";

export async function loader({ context }: LoaderFunctionArgs) {
  try {
    // Check if DATABASE_URL is available
    if (!context.cloudflare?.env?.DATABASE_URL) {
      return json(
        {
          status: "error",
          message: "DATABASE_URL not configured",
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

    console.log("Testing database connection...");

    const db = createDb({
      url: context.cloudflare.env.DATABASE_URL,
      connectionTimeoutMillis: 5000, // 5 seconds for health check
      enableLogging: true,
    });

    // Test basic connection with a simple query
    const result = await db.execute("SELECT 1 as test");

    console.log("Database connection successful:", result);

    return json({
      status: "healthy",
      message: "Database connection successful",
      timestamp: new Date().toISOString(),
      testResult: result,
    });
  } catch (error) {
    console.error("Database health check failed:", error);

    return json(
      {
        status: "unhealthy",
        message: error instanceof Error ? error.message : "Unknown database error",
        timestamp: new Date().toISOString(),
        error: {
          name: error instanceof Error ? error.name : "UnknownError",
          message: error instanceof Error ? error.message : "Unknown error occurred",
        },
      },
      { status: 500 }
    );
  }
}
