import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { generateText } from "ai";
import type { AIProvider } from "~/lib/ai/ai-providers";
import {
  AI_ERROR_MESSAGES,
  createAIModelSafely,
  getAIErrorMessage,
  logAIOperation,
  sanitizePromptForLogging,
  validateAIRequest,
} from "~/lib/ai/ai-utils";
import { respData, respErr } from "~/lib/api/resp";
import { createDb } from "~/lib/db/db";
import { authenticateApiRequest } from "~/lib/middleware/api-auth.server";
import { trackApiUsage } from "~/services/analytics.server";
import {
  CreditsAmount,
  CreditsTransType,
  decreaseCredits,
  getUserUuid,
} from "~/services/user-management.server";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  const startTime = Date.now();
  let provider: AIProvider | undefined;
  let model: string | undefined;
  let userUuid: string | undefined;
  let db: any;

  try {
    // Create database connection
    if (!context.cloudflare?.env?.DATABASE_URL) {
      return respErr("Database not available");
    }
    db = createDb(context.cloudflare.env.DATABASE_URL);
    // Parse request body
    const body = await request.json();

    // Validate request parameters
    if (!validateAIRequest(body)) {
      return respErr(AI_ERROR_MESSAGES.INVALID_PARAMS);
    }

    const { prompt, provider: reqProvider, model: reqModel } = body;
    provider = reqProvider;
    model = reqModel;

    // Check user authentication (session or API key)
    let user_uuid = await getUserUuid();

    // If no session authentication, try API key authentication
    if (!user_uuid) {
      const apiAuth = await authenticateApiRequest(request, db);
      if (apiAuth.isAuthenticated && apiAuth.userUuid) {
        user_uuid = apiAuth.userUuid;
      } else {
        return respErr("Authentication required. Please provide a valid session or API key.");
      }
    }

    userUuid = user_uuid;

    // Log the operation start
    console.log("Starting AI text generation:", {
      provider,
      model,
      prompt: sanitizePromptForLogging(prompt),
      user_uuid,
      timestamp: new Date().toISOString(),
    });

    // Create AI model
    const textModel = createAIModelSafely(provider, model, context);

    // Generate text
    const result = await generateText({
      model: textModel,
      prompt: prompt,
      maxTokens: 4000, // Reasonable limit
      temperature: 0.7, // Balanced creativity
    });

    // Check for warnings
    if (result.warnings && result.warnings.length > 0) {
      console.warn("AI generation warnings:", {
        provider,
        model,
        warnings: result.warnings,
      });

      // Don't fail on warnings, but log them
      logAIOperation("generate-text", provider, model, true, Date.now() - startTime, {
        warnings: result.warnings,
      });
    }

    // Decrease user credits after successful generation
    try {
      await decreaseCredits(
        {
          user_uuid,
          trans_type: CreditsTransType.AITextGeneration,
          credits: CreditsAmount.AITextGenerationCost,
        },
        db
      );
    } catch (creditError) {
      console.error("Failed to decrease credits:", creditError);
      // Continue with the response even if credit deduction fails
    }

    // Log successful operation
    logAIOperation("generate-text", provider, model, true, Date.now() - startTime);

    // Track successful usage
    if (userUuid) {
      try {
        await trackApiUsage(
          {
            userUuid,
            endpoint: "/api/ai/generate-text",
            method: "POST",
            provider,
            model,
            tokensUsed: result.usage?.totalTokens || 0,
            creditsUsed: CreditsAmount.AITextGenerationCost,
            duration: Date.now() - startTime,
            status: "success",
            metadata: {
              finishReason: result.finishReason,
              usage: result.usage,
              warnings: result.warnings,
            },
          },
          db
        );
      } catch (trackingError) {
        console.error("Failed to track usage:", trackingError);
        // Continue with response even if tracking fails
      }
    }

    // Return successful response
    return respData({
      text: result.text,
      reasoning: result.reasoning || null,
      usage: result.usage || null,
      finishReason: result.finishReason || null,
      provider,
      model,
    });
  } catch (error) {
    // Log failed operation
    if (provider && model) {
      logAIOperation("generate-text", provider, model, false, Date.now() - startTime, error);
    }

    console.error("AI text generation failed:", {
      error: (error as any)?.message || error,
      provider,
      model,
      stack: (error as any)?.stack,
    });

    // Track failed usage
    if (userUuid) {
      try {
        await trackApiUsage(
          {
            userUuid,
            endpoint: "/api/ai/generate-text",
            method: "POST",
            provider,
            model,
            duration: Date.now() - startTime,
            status: "error",
            errorCode: (error as any)?.code || "AI_GENERATION_ERROR",
            errorMessage: (error as any)?.message || "Unknown error",
            metadata: {
              stack: (error as any)?.stack,
            },
          },
          db
        );
      } catch (trackingError) {
        console.error("Failed to track error usage:", trackingError);
      }
    }

    // Return user-friendly error message
    const errorMessage = getAIErrorMessage(error);
    return respErr(errorMessage);
  }
}
