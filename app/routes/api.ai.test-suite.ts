import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import type { TestCase } from "~/lib/ai/ai-test-runner";
import {
  DEFAULT_TEST_CASES,
  runTestCase,
  runTestSuite,
  validateTestEnvironment,
} from "~/lib/ai/ai-test-runner";
import { respData, respErr } from "~/lib/api/resp";

// Type definitions for request body
interface AITestSuiteBody {
  action:
    | "validate-environment"
    | "run-single-test"
    | "run-test-suite"
    | "run-provider-tests"
    | "run-performance-test"
    | "health-check";
  testCases?: TestCase[];
  singleTest?: {
    provider: string;
    model: string;
    testType: string;
    prompt: string;
  };
  provider?: string;
  iterations?: number;
  concurrency?: number;
}

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const body = (await request.json()) as AITestSuiteBody;
    const { action: testAction, testCases, singleTest } = body;

    // Get base URL for internal API calls
    const baseUrl = new URL(request.url).origin;

    switch (testAction) {
      case "validate-environment": {
        // Check environment variables
        const env = context.cloudflare?.env;
        const envStatus = {
          OPENAI_API_KEY: !!env?.OPENAI_API_KEY,
          DEEPSEEK_API_KEY: !!env?.DEEPSEEK_API_KEY,
          OPENROUTER_API_KEY: !!env?.OPENROUTER_API_KEY,
          SILICONFLOW_API_KEY: !!env?.SILICONFLOW_API_KEY,
          REPLICATE_API_TOKEN: !!env?.REPLICATE_API_TOKEN,
        };

        const validation = validateTestEnvironment(envStatus);

        return respData({
          envStatus,
          validation,
          timestamp: new Date().toISOString(),
        });
      }

      case "run-single-test": {
        if (!singleTest) {
          return respErr("Single test configuration required");
        }

        const testCase: TestCase = {
          id: `manual-${Date.now()}`,
          name: "Manual Test",
          description: "User-initiated test",
          provider: singleTest.provider,
          model: singleTest.model,
          testType: singleTest.testType,
          prompt: singleTest.prompt,
          shouldSucceed: true,
        };

        const result = await runTestCase(testCase, baseUrl);

        return respData({
          result,
          timestamp: new Date().toISOString(),
        });
      }

      case "run-test-suite": {
        const casesToRun = testCases || DEFAULT_TEST_CASES;

        // Filter test cases based on available environment variables
        const env = context.cloudflare?.env;
        const filteredCases = casesToRun.filter((testCase: TestCase) => {
          switch (testCase.provider) {
            case "openai":
              return !!env?.OPENAI_API_KEY;
            case "deepseek":
              return !!env?.DEEPSEEK_API_KEY;
            case "openrouter":
              return !!env?.OPENROUTER_API_KEY;
            case "siliconflow":
              return !!env?.SILICONFLOW_API_KEY;
            case "replicate":
              return !!env?.REPLICATE_API_TOKEN;
            default:
              return false;
          }
        });

        if (filteredCases.length === 0) {
          return respErr("No test cases can be run with current environment configuration");
        }

        const suiteResult = await runTestSuite(filteredCases, baseUrl);

        return respData({
          suiteResult,
          skippedTests: casesToRun.length - filteredCases.length,
          timestamp: new Date().toISOString(),
        });
      }

      case "run-provider-tests": {
        const { provider } = body;
        if (!provider) {
          return respErr("Provider required for provider-specific tests");
        }

        // Filter test cases for specific provider
        const providerCases = DEFAULT_TEST_CASES.filter((tc) => tc.provider === provider);

        if (providerCases.length === 0) {
          return respErr(`No test cases found for provider: ${provider}`);
        }

        const suiteResult = await runTestSuite(providerCases, baseUrl);

        return respData({
          suiteResult,
          provider,
          timestamp: new Date().toISOString(),
        });
      }

      case "run-performance-test": {
        const { iterations = 5, concurrency = 2 } = body;

        // Simple performance test with OpenAI GPT-4o-mini
        const performanceTestCase: TestCase = {
          id: "performance-test",
          name: "Performance Test",
          description: "Performance and load testing",
          provider: "openai",
          model: "gpt-4o-mini",
          testType: "text",
          prompt: "Hello, world!",
          shouldSucceed: true,
        };

        const results = [];
        const startTime = Date.now();

        // Run tests with controlled concurrency
        for (let i = 0; i < iterations; i += concurrency) {
          const batch = [];
          for (let j = 0; j < concurrency && i + j < iterations; j++) {
            batch.push(runTestCase(performanceTestCase, baseUrl));
          }

          const batchResults = await Promise.all(batch);
          results.push(...batchResults);

          // Small delay between batches
          if (i + concurrency < iterations) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }
        }

        const totalDuration = Date.now() - startTime;
        const successfulTests = results.filter((r) => r.success).length;
        const averageDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
        const minDuration = Math.min(...results.map((r) => r.duration));
        const maxDuration = Math.max(...results.map((r) => r.duration));

        return respData({
          performanceResults: {
            totalTests: results.length,
            successfulTests,
            failedTests: results.length - successfulTests,
            totalDuration,
            averageDuration: Math.round(averageDuration),
            minDuration,
            maxDuration,
            throughput: Math.round((results.length / totalDuration) * 1000 * 60), // tests per minute
            results,
          },
          timestamp: new Date().toISOString(),
        });
      }

      case "health-check": {
        // Quick health check for all providers
        const healthChecks = [];
        const env = context.cloudflare?.env;

        const quickTests: TestCase[] = [
          {
            id: "health-openai",
            name: "OpenAI Health Check",
            description: "Quick OpenAI connectivity test",
            provider: "openai",
            model: "gpt-4o-mini",
            testType: "text",
            prompt: "Hi",
            shouldSucceed: true,
          },
          {
            id: "health-deepseek",
            name: "DeepSeek Health Check",
            description: "Quick DeepSeek connectivity test",
            provider: "deepseek",
            model: "deepseek-chat",
            testType: "text",
            prompt: "Hi",
            shouldSucceed: true,
          },
        ];

        for (const testCase of quickTests) {
          // Only run if API key is available
          const hasApiKey =
            testCase.provider === "openai" ? !!env?.OPENAI_API_KEY : !!env?.DEEPSEEK_API_KEY;

          if (hasApiKey) {
            try {
              const result = await runTestCase(testCase, baseUrl);
              healthChecks.push({
                provider: testCase.provider,
                status: result.success ? "healthy" : "unhealthy",
                duration: result.duration,
                error: result.error,
              });
            } catch (error) {
              healthChecks.push({
                provider: testCase.provider,
                status: "error",
                error: error instanceof Error ? error.message : "Unknown error",
              });
            }
          } else {
            healthChecks.push({
              provider: testCase.provider,
              status: "no-api-key",
              error: "API key not configured",
            });
          }
        }

        return respData({
          healthChecks,
          timestamp: new Date().toISOString(),
        });
      }

      default:
        return respErr("Invalid test action");
    }
  } catch (error) {
    console.error("AI Test Suite API error:", error);
    return respErr(error instanceof Error ? error.message : "Test suite execution failed");
  }
}

// Also support GET for health checks
export async function loader({ context }: ActionFunctionArgs) {
  // Simple health check endpoint
  const env = context.cloudflare?.env;
  const envStatus = {
    OPENAI_API_KEY: !!env?.OPENAI_API_KEY,
    DEEPSEEK_API_KEY: !!env?.DEEPSEEK_API_KEY,
    OPENROUTER_API_KEY: !!env?.OPENROUTER_API_KEY,
    SILICONFLOW_API_KEY: !!env?.SILICONFLOW_API_KEY,
    REPLICATE_API_TOKEN: !!env?.REPLICATE_API_TOKEN,
  };

  const validation = validateTestEnvironment(envStatus);

  return json({
    status: "ok",
    envStatus,
    validation,
    availableActions: [
      "validate-environment",
      "run-single-test",
      "run-test-suite",
      "run-provider-tests",
      "run-performance-test",
      "health-check",
    ],
    timestamp: new Date().toISOString(),
  });
}
