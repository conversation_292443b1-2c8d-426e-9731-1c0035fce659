import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { requireUser } from "~/lib/auth/middleware.server";
import { findUserByInviteCode, findUserByUuid, updateUserInviteCode } from "~/models/user";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    const body = (await request.json()) as { invite_code?: string };
    const { invite_code } = body;

    if (!invite_code) {
      return respErr("invalid params");
    }

    if (invite_code.length < 2 || invite_code.length > 16) {
      return respErr("invalid invite code, length must be between 2 and 16");
    }

    const authUser = await requireUser(request);
    const user_uuid = authUser.id;

    const user_info = await findUserByUuid(user_uuid, db);
    if (!user_info || !user_info.email) {
      return respErr("invalid user");
    }

    if (user_info.invite_code === invite_code) {
      return respData(user_info);
    }

    // Check if invite code is already taken
    const existingUser = await findUserByInviteCode(invite_code, db);
    if (existingUser && existingUser.uuid !== user_uuid) {
      return respErr("invite code already taken");
    }

    // Update invite code
    const success = await updateUserInviteCode(user_uuid, invite_code, db);
    if (!success) {
      return respErr("failed to update invite code");
    }

    // Return updated user info
    const updatedUser = await findUserByUuid(user_uuid, db);
    return respData(updatedUser);
  } catch (e) {
    console.log("update invite code failed", e);
    return respErr("update invite code failed");
  }
}
