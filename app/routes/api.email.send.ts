import { type ActionFunctionArgs, json } from "@remix-run/cloudflare";
import { z } from "zod";
import { emailService } from "~/lib/email/service.server";
import { getTemplate, type TemplateName } from "~/lib/email/templates";
import type { EmailRecipient } from "~/lib/email/types";

// Define the schema for the request body
const sendEmailPayloadSchema = z.object({
  templateName: z.custom<TemplateName>(
    (val) => typeof val === "string" && ["welcome", "passwordReset"].includes(val),
    { message: "Invalid template name. Must be 'welcome' or 'passwordReset'." }
  ),
  to: z.object({
    email: z.string().email(),
    name: z.string().optional(),
  }),
  from: z
    .object({
      // Although Resend might use a default, let's allow specifying for flexibility
      email: z.string().email(),
      name: z.string().optional(),
    })
    .optional(), // Making 'from' optional at payload, service might have default
  templateVariables: z
    .record(z.union([z.string(), z.number()]))
    .optional()
    .default({}),
});

export const action = async ({ request, context }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  let payload;
  try {
    const rawPayload = await request.json();
    payload = sendEmailPayloadSchema.parse(rawPayload);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return json({ error: "Invalid request payload", details: error.issues }, { status: 400 });
    }
    return json({ error: "Failed to parse request body" }, { status: 400 });
  }

  const { templateName, to, templateVariables } = payload;

  // Determine 'from' address: use payload.from if provided, otherwise a default.
  // IMPORTANT: The 'from' email address MUST be a verified domain in your Resend account.
  // Using a placeholder here. Replace with your actual verified sender email.
  const fromAddress: EmailRecipient = payload.from || {
    email: "<EMAIL>",
    name: "Your Platform",
  };

  const template = getTemplate(templateName);

  if (!template) {
    return json({ error: `Template '${templateName}' not found.` }, { status: 404 });
  }

  try {
    const subject = template.subject(templateVariables);
    const html = template.html(templateVariables);
    const text = template.text(templateVariables);

    const result = await emailService.sendEmail({
      to,
      from: fromAddress,
      subject,
      html,
      text,
    });

    if (result.success) {
      return json(
        { message: "Email sent successfully", messageId: result.messageId },
        { status: 200 }
      );
    } else {
      // Log the detailed error on the server
      console.error(
        `Failed to send email. Template: ${templateName}, To: ${to.email}, Error: ${result.error}`
      );
      return json({ error: "Failed to send email", details: result.error }, { status: 500 });
    }
  } catch (error: any) {
    console.error("Error processing email request:", error);
    return json(
      { error: "Internal server error while sending email", details: error.message },
      { status: 500 }
    );
  }
};

// Optional: Add a loader if you want to check something via GET, though typically not needed for a 'send' action.
// export const loader = async () => {
//   return json({ message: "API route for sending email. Use POST." });
// };
