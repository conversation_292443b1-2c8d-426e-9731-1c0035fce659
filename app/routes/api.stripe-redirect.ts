/**
 * API Route: Stripe Redirect
 * Handles redirects to Stripe checkout sessions
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { getStripeClient } from "~/lib/payment/stripe.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get("session_id");

    if (!sessionId) {
      return redirect("/pricing?error=missing_session");
    }

    const stripeSecretKey = context.cloudflare?.env?.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      return redirect("/pricing?error=stripe_not_configured");
    }

    const stripe = getStripeClient(stripeSecretKey);

    // Retrieve the checkout session to get the URL
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    if (!session.url) {
      return redirect("/pricing?error=invalid_session");
    }

    // Redirect to Stripe checkout
    return redirect(session.url);
  } catch (error) {
    console.error("Error redirecting to Stripe:", error);
    return redirect("/pricing?error=redirect_failed");
  }
}
