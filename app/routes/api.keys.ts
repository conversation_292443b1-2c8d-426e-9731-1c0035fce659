/**
 * API Route: API Keys Management
 * Handles API key CRUD operations via REST API
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { createDbFromEnv } from "~/lib/db";
import {
  type CreateApiKeyParams,
  createApi<PERSON>ey,
  deleteApi<PERSON><PERSON>,
  getApi<PERSON>eyById,
  getUserApiKeys,
  revokeApiKey,
  updateApiKeyTitle,
} from "~/services/api-key.server";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    const url = new URL(request.url);
    const action = url.searchParams.get("action");
    const apiKeyId = url.searchParams.get("id");

    switch (action) {
      case "list": {
        const page = parseInt(url.searchParams.get("page") || "1", 10);
        const limit = parseInt(url.searchParams.get("limit") || "20", 10);
        const includeRevoked = url.searchParams.get("include_revoked") === "true";

        const result = await getUserApiKeys(userUuid, { page, limit, includeRevoked }, db);
        return respData(result);
      }

      case "get": {
        if (!apiKeyId) {
          return respErr("API key ID is required");
        }

        const apiKey = await getApiKeyById(parseInt(apiKeyId, 10), userUuid, db);
        if (!apiKey) {
          return respErr("API key not found");
        }

        return respData({ apiKey });
      }

      default:
        return respErr("Invalid action. Supported actions: list, get");
    }
  } catch (error) {
    console.error("Error in API keys API:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process request");
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "create": {
        const { title, expiresAt } = body as CreateApiKeyParams & { expiresAt?: string };

        if (!title) {
          return respErr("Title is required");
        }

        const createParams: CreateApiKeyParams = {
          title,
          userUuid,
          expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        };

        const result = await createApiKey(createParams, db);

        if (result.success) {
          return respData({
            message: "API key created successfully",
            apiKey: result.apiKey,
          });
        } else {
          return respErr(result.error || "Failed to create API key");
        }
      }

      case "revoke": {
        const { apiKeyId } = body;

        if (!apiKeyId) {
          return respErr("API key ID is required");
        }

        const result = await revokeApiKey(apiKeyId, userUuid, db);

        if (result.success) {
          return respData({ message: "API key revoked successfully" });
        } else {
          return respErr(result.error || "Failed to revoke API key");
        }
      }

      case "delete": {
        const { apiKeyId } = body;

        if (!apiKeyId) {
          return respErr("API key ID is required");
        }

        const result = await deleteApiKey(apiKeyId, userUuid, db);

        if (result.success) {
          return respData({ message: "API key deleted successfully" });
        } else {
          return respErr(result.error || "Failed to delete API key");
        }
      }

      case "update": {
        const { apiKeyId, title } = body;

        if (!apiKeyId) {
          return respErr("API key ID is required");
        }

        if (!title) {
          return respErr("Title is required");
        }

        const result = await updateApiKeyTitle(apiKeyId, userUuid, title, db);

        if (result.success) {
          return respData({ message: "API key updated successfully" });
        } else {
          return respErr(result.error || "Failed to update API key");
        }
      }

      default:
        return respErr("Invalid action. Supported actions: create, revoke, delete, update");
    }
  } catch (error) {
    console.error("Error in API keys API action:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process request");
  }
}
