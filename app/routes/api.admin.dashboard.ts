import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { and, count, desc, eq, gte, lte, sql, sum } from "drizzle-orm";
import { respData, respErr } from "~/lib/api/resp";
import { creditTransactions, feedback, orders, users } from "~/lib/db/schema";

// Type definitions for request body
interface AdminDashboardBody {
  action:
    | "get-overview-stats"
    | "get-recent-users"
    | "get-recent-orders"
    | "get-recent-feedback"
    | "get-ai-usage-stats"
    | "get-system-health";
  timeRange?: string;
  limit?: number;
}

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    const body = (await request.json()) as AdminDashboardBody;
    const { action: dashboardAction, timeRange = "30d" } = body;

    // Calculate date range
    const now = new Date();
    const startDate = new Date();
    switch (timeRange) {
      case "7d":
        startDate.setDate(now.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(now.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(now.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    switch (dashboardAction) {
      case "get-overview-stats": {
        // Get total users
        const totalUsersResult = await db.select({ count: count() }).from(users);
        const totalUsers = totalUsersResult[0]?.count || 0;

        // Get new users in time range
        const newUsersResult = await db
          .select({ count: count() })
          .from(users)
          .where(gte(users.createdAt, startDate));
        const newUsers = newUsersResult[0]?.count || 0;

        // Get total orders
        const totalOrdersResult = await db.select({ count: count() }).from(orders);
        const totalOrders = totalOrdersResult[0]?.count || 0;

        // Get paid orders in time range
        const paidOrdersResult = await db
          .select({ count: count() })
          .from(orders)
          .where(and(eq(orders.status, "paid"), gte(orders.createdAt, startDate)));
        const paidOrders = paidOrdersResult[0]?.count || 0;

        // Get total revenue
        const revenueResult = await db
          .select({
            total: sql<number>`COALESCE(SUM(CAST(${orders.amount} AS DECIMAL)), 0)`,
          })
          .from(orders)
          .where(and(eq(orders.status, "paid"), gte(orders.createdAt, startDate)));
        const totalRevenue = revenueResult[0]?.total || 0;

        // Get total feedback
        const totalFeedbackResult = await db.select({ count: count() }).from(feedback);
        const totalFeedback = totalFeedbackResult[0]?.count || 0;

        // Get new feedback in time range
        const newFeedbackResult = await db
          .select({ count: count() })
          .from(feedback)
          .where(gte(feedback.createdAt, startDate));
        const newFeedback = newFeedbackResult[0]?.count || 0;

        // Get total AI requests (credit transactions)
        const aiRequestsResult = await db
          .select({ count: count() })
          .from(creditTransactions)
          .where(
            and(
              sql`${creditTransactions.transType} IN ('ai_text_generation', 'ai_image_generation', 'ai_stream_text')`,
              gte(creditTransactions.createdAt, startDate)
            )
          );
        const aiRequests = aiRequestsResult[0]?.count || 0;

        // Calculate growth percentages (mock for now, would need historical data)
        const userGrowth = totalUsers > 0 ? ((newUsers / totalUsers) * 100).toFixed(1) : "0";
        const orderGrowth = totalOrders > 0 ? ((paidOrders / totalOrders) * 100).toFixed(1) : "0";
        const revenueGrowth = "8.2"; // Mock value
        const aiGrowth = aiRequests > 0 ? "23.1" : "0";

        return respData({
          stats: {
            totalUsers: totalUsers.toLocaleString(undefined),
            userGrowth: `+${userGrowth}%`,
            totalRevenue: `$${totalRevenue.toLocaleString(undefined)}`,
            revenueGrowth: `+${revenueGrowth}%`,
            activeOrders: paidOrders.toString(),
            orderGrowth: orderGrowth.startsWith("-") ? orderGrowth : `+${orderGrowth}%`,
            aiRequests: aiRequests.toLocaleString(),
            aiGrowth: `+${aiGrowth}%`,
            totalFeedback: totalFeedback.toString(),
            newFeedback: newFeedback.toString(),
          },
          timeRange,
          timestamp: new Date().toISOString(),
        });
      }

      case "get-recent-users": {
        const recentUsers = await db
          .select({
            id: users.id,
            uuid: users.uuid,
            name: users.name,
            email: users.email,
            avatar: users.avatar,
            credits: users.credits,
            createdAt: users.createdAt,
          })
          .from(users)
          .orderBy(desc(users.createdAt))
          .limit(10);

        return respData({
          users: recentUsers.map((user) => ({
            id: user.id,
            uuid: user.uuid,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
            credits: user.credits || 0,
            joinedAt: user.createdAt.toISOString(),
            plan: user.credits && user.credits > 100 ? "Pro" : "Free", // Mock plan logic
          })),
          timestamp: new Date().toISOString(),
        });
      }

      case "get-recent-orders": {
        const recentOrders = await db
          .select({
            id: orders.id,
            orderNo: orders.orderNo,
            userEmail: orders.userEmail,
            amount: orders.amount,
            currency: orders.currency,
            productName: orders.productName,
            status: orders.status,
            createdAt: orders.createdAt,
          })
          .from(orders)
          .orderBy(desc(orders.createdAt))
          .limit(10);

        return respData({
          orders: recentOrders.map((order) => ({
            id: order.id,
            orderNo: order.orderNo,
            userEmail: order.userEmail,
            amount: parseFloat(order.amount),
            currency: order.currency,
            productName: order.productName,
            status: order.status,
            createdAt: order.createdAt.toISOString(),
          })),
          timestamp: new Date().toISOString(),
        });
      }

      case "get-recent-feedback": {
        const recentFeedback = await db
          .select({
            id: feedback.id,
            userUuid: feedback.userUuid,
            content: feedback.content,
            rating: feedback.rating,
            status: feedback.status,
            createdAt: feedback.createdAt,
          })
          .from(feedback)
          .orderBy(desc(feedback.createdAt))
          .limit(10);

        return respData({
          feedback: recentFeedback.map((item) => ({
            id: item.id,
            userUuid: item.userUuid,
            content: item.content,
            rating: item.rating,
            status: item.status,
            createdAt: item.createdAt.toISOString(),
          })),
          timestamp: new Date().toISOString(),
        });
      }

      case "get-ai-usage-stats": {
        // Get AI usage by type
        const aiUsageStats = await db
          .select({
            transType: creditTransactions.transType,
            count: count(),
            totalCredits: sum(creditTransactions.credits),
          })
          .from(creditTransactions)
          .where(
            and(
              sql`${creditTransactions.transType} IN ('ai_text_generation', 'ai_image_generation', 'ai_stream_text')`,
              gte(creditTransactions.createdAt, startDate)
            )
          )
          .groupBy(creditTransactions.transType);

        return respData({
          aiUsage: aiUsageStats.map((stat) => ({
            type: stat.transType,
            requests: stat.count,
            creditsUsed: Math.abs(Number(stat.totalCredits) || 0),
          })),
          timeRange,
          timestamp: new Date().toISOString(),
        });
      }

      case "get-system-health": {
        // Mock system health data (in real app, would check actual system metrics)
        const health = {
          database: "healthy",
          api: "healthy",
          storage: "healthy",
          ai_providers: {
            openai: "healthy",
            deepseek: "healthy",
            openrouter: "warning",
            siliconflow: "healthy",
          },
          uptime: "99.9%",
          responseTime: "120ms",
          errorRate: "0.1%",
        };

        return respData({
          health,
          timestamp: new Date().toISOString(),
        });
      }

      default:
        return respErr("Invalid dashboard action");
    }
  } catch (error) {
    console.error("Admin dashboard API error:", error);
    return respErr(error instanceof Error ? error.message : "Dashboard data fetch failed");
  }
}

// Also support GET for basic health check
export async function loader({ context }: ActionFunctionArgs) {
  const db = context.db;

  return json({
    status: "ok",
    database: !!db,
    availableActions: [
      "get-overview-stats",
      "get-recent-users",
      "get-recent-orders",
      "get-recent-feedback",
      "get-ai-usage-stats",
      "get-system-health",
    ],
    timestamp: new Date().toISOString(),
  });
}
