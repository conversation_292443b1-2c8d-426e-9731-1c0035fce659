import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { createDbFromEnv } from "~/lib/db";
import { getOrderAnalytics, searchOrders } from "~/models/order";

// Type definitions for request body
interface OrderSearchBody {
  action?: "analytics" | "search";
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  userUuid?: string;
  status?: string[];
  minAmount?: number;
  maxAmount?: number;
  dateFrom?: string;
  dateTo?: string;
  accountId?: string;
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Create database connection
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // Parse search parameters
    const url = new URL(request.url);
    const searchParams = {
      search: url.searchParams.get("search") || undefined,
      page: parseInt(url.searchParams.get("page") || "1"),
      limit: parseInt(url.searchParams.get("limit") || "10"),
      sortBy: url.searchParams.get("sortBy") || "createdAt",
      sortOrder: (url.searchParams.get("sortOrder") as "asc" | "desc") || "desc",
      userUuid: url.searchParams.get("userUuid") || undefined,
      status: url.searchParams.get("status")?.split(",") || undefined,
      minAmount: url.searchParams.get("minAmount")
        ? parseFloat(url.searchParams.get("minAmount")!)
        : undefined,
      maxAmount: url.searchParams.get("maxAmount")
        ? parseFloat(url.searchParams.get("maxAmount")!)
        : undefined,
      dateFrom: url.searchParams.get("dateFrom")
        ? new Date(url.searchParams.get("dateFrom")!)
        : undefined,
      dateTo: url.searchParams.get("dateTo")
        ? new Date(url.searchParams.get("dateTo")!)
        : undefined,
    };

    // Validate pagination parameters
    if (searchParams.page < 1) searchParams.page = 1;
    if (searchParams.limit < 1 || searchParams.limit > 100) searchParams.limit = 10;

    // Search orders with the new enhanced search function
    const result = await searchOrders(db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("Error searching orders:", error);
    return json(
      {
        success: false,
        error: "Failed to search orders",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST method for more complex search queries and analytics
export async function action({ request, context }: LoaderFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // Parse request body
    const body = (await request.json()) as OrderSearchBody;

    // Handle analytics request
    if (body.action === "analytics") {
      const analyticsParams = {
        dateFrom: body.dateFrom ? new Date(body.dateFrom) : undefined,
        dateTo: body.dateTo ? new Date(body.dateTo) : undefined,
        accountId: body.accountId,
      };

      const analytics = await getOrderAnalytics(db, analyticsParams);

      return json({
        success: true,
        data: analytics,
      });
    }

    // Handle search request
    const searchParams = {
      search: body.search || undefined,
      page: body.page || 1,
      limit: body.limit || 10,
      sortBy: body.sortBy || "createdAt",
      sortOrder: body.sortOrder || "desc",
      userUuid: body.userUuid,
      status: body.status,
      minAmount: body.minAmount,
      maxAmount: body.maxAmount,
      dateFrom: body.dateFrom ? new Date(body.dateFrom) : undefined,
      dateTo: body.dateTo ? new Date(body.dateTo) : undefined,
    };

    // Validate pagination parameters
    if (searchParams.page < 1) searchParams.page = 1;
    if (searchParams.limit < 1 || searchParams.limit > 100) searchParams.limit = 10;

    // Search orders
    const result = await searchOrders(db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("Error processing order request:", error);
    return json(
      {
        success: false,
        error: "Failed to process order request",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
