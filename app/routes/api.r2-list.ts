/**
 * R2 File List API - Simplified Version
 * Direct use of Cloudflare Workers R2 binding
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";

// Format file size
function formatFileSize(bytes: number): string {
  const units = ["B", "KB", "MB", "GB"];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Direct use of R2 binding
    const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
    if (!R2_BUCKET) {
      return json({ error: "R2 bucket not configured" }, { status: 503 });
    }

    const url = new URL(request.url);
    const prefix = url.searchParams.get("prefix") || "";
    const limit = parseInt(url.searchParams.get("limit") || "100", 10);

    // List files from R2
    const result = await R2_BUCKET.list({
      prefix,
      limit: Math.min(limit, 1000),
    });

    // Enhance file information
    const files = result.objects.map((obj) => ({
      key: obj.key,
      size: obj.size,
      lastModified: obj.uploaded,
      etag: obj.etag,
      contentType: obj.httpMetadata?.contentType,
      formattedSize: formatFileSize(obj.size),
      filename: obj.key.split("/").pop() || obj.key,
      downloadUrl: `/api/r2-download?key=${encodeURIComponent(obj.key)}`,
      isImage: obj.httpMetadata?.contentType?.startsWith("image/") || false,
    }));

    // Calculate statistics
    const summary = {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      formattedTotalSize: formatFileSize(files.reduce((sum, file) => sum + file.size, 0)),
    };

    return json({
      success: true,
      files,
      summary,
      pagination: {
        truncated: result.truncated,
        hasMore: result.truncated,
      },
    });
  } catch (error) {
    console.error("R2 list error:", error);
    return json(
      {
        error: "Failed to list files",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * Handle file deletion
 */
export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "DELETE") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
    if (!R2_BUCKET) {
      return json({ error: "R2 bucket not configured" }, { status: 503 });
    }

    const url = new URL(request.url);
    const key = url.searchParams.get("key");

    if (!key) {
      return json({ error: "Missing file key" }, { status: 400 });
    }

    // Delete file from R2
    await R2_BUCKET.delete(key);

    return json({ success: true, deleted: key });
  } catch (error) {
    console.error("R2 delete error:", error);
    return json(
      {
        error: "Delete failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
